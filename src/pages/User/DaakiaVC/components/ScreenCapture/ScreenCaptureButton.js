import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { CameraOutlined } from "@ant-design/icons";
import { toPng } from "html-to-image";
import { Track } from "livekit-client";
import { DataReceivedEvent } from "../../utils/constants";
import "./ScreenCaptureButton.scss";

export function ScreenCaptureButton({
  trackRef,
  room,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  const [isCapturing, setIsCapturing] = useState(false);

  // Only show button for screen share tracks
  if (!trackRef || trackRef.source !== Track.Source.ScreenShare) {
    return null;
  }

  // This function is now deprecated in favor of captureVideoElement
  // Keeping it as fallback for html-to-image approach if needed
  const captureElement = async (element) => {
    try {
      // Find video element within the element and capture only that
      const videoElement = element.querySelector('video');
      if (videoElement) {
        return await captureVideoElement(videoElement);
      }

      throw new Error("No video element found for capture");

    } catch (error) {
      console.error("Capture element error:", error);
      throw error;
    }
  };

  const captureVideoElement = async (videoElement) => {
    try {
      // Validate video element
      if (!videoElement || !videoElement.videoWidth || !videoElement.videoHeight) {
        throw new Error("Invalid video element: no dimensions or not loaded");
      }

      console.log("Capturing video element:", videoElement, "Dimensions:", videoElement.videoWidth, "x", videoElement.videoHeight);

      // Create canvas to capture the video frame
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video's actual dimensions
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;

      // Draw the current video frame to canvas
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob and download
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error("Failed to create blob from video canvas"));
            return;
          }

          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.download = `screen-share-capture-${Date.now()}.png`;
          link.href = url;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          console.log("Video capture successful");
          resolve();
        }, 'image/png', 0.95);
      });

    } catch (error) {
      console.error("Video element capture error:", error);
      throw new Error(`Video capture failed: ${error.message}`);
    }
  };

  const captureWithCanvas = async (element) => {
    try {
      // Find video element within the target element
      const video = element.querySelector('video');
      if (!video || !video.videoWidth || !video.videoHeight) {
        throw new Error("No valid video element found for canvas capture");
      }

      console.log("Using canvas fallback for video:", video, "Dimensions:", video.videoWidth, "x", video.videoHeight);

      // Create canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to blob and download
      canvas.toBlob((blob) => {
        if (!blob) {
          throw new Error("Failed to create blob from canvas");
        }

        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.download = `screen-capture-${Date.now()}.png`;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }, 'image/png', 0.95);

    } catch (error) {
      console.error("Canvas capture error:", error);
      throw new Error(`Canvas capture failed: ${error.message}`);
    }
  };

  const broadcastCaptureEvent = async () => {
    if (!room || !room.localParticipant) return;

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.SCREEN_CAPTURE_TAKEN,
        participantName: room.localParticipant.name || room.localParticipant.identity,
        timestamp: Date.now(),
      })
    );

    room.localParticipant.publishData(data, {
      reliable: true,
    });
  };

  const handleScreenCapture = async () => {
    if (isCapturing) return;

    setIsCapturing(true);

    try {
      // Find the screen share tile element - multiple strategies
      let targetElement = null;

      console.log("Looking for screen share element...");

      // Strategy 1: Find the current component's parent tile (most reliable)
      if (trackRef && trackRef.participant) {
        const participantTiles = document.querySelectorAll('.lk-participant-tile, [data-lk-participant]');
        for (const tile of participantTiles) {
          // Look for screen share indicators in this tile
          const hasScreenShareIcon = tile.querySelector('svg[data-testid="screen-share-icon"], .lk-screen-share-icon');
          const hasScreenText = tile.textContent && tile.textContent.includes('screen');
          const hasScreenVideo = tile.querySelector('video');

          if (hasScreenShareIcon || hasScreenText) {
            targetElement = tile;
            console.log("Found screen share tile via icon/text:", tile);
            break;
          }

          // Check if this tile has a screen share video
          if (hasScreenVideo && hasScreenVideo.srcObject) {
            const tracks = hasScreenVideo.srcObject.getVideoTracks();
            for (const track of tracks) {
              if (track.label && (
                track.label.includes('screen') ||
                track.label.includes('Screen') ||
                track.label.includes('display') ||
                track.label.includes('Display')
              )) {
                targetElement = tile;
                console.log("Found screen share tile via video track:", tile);
                break;
              }
            }
            if (targetElement) break;
          }
        }
      }

      // Strategy 2: Find by video element with screen share track
      if (!targetElement) {
        const videoElements = document.querySelectorAll('video');
        for (const video of videoElements) {
          if (video.srcObject && video.srcObject.getVideoTracks) {
            const tracks = video.srcObject.getVideoTracks();
            for (const track of tracks) {
              if (track.label && (
                track.label.includes('screen') ||
                track.label.includes('Screen') ||
                track.label.includes('display') ||
                track.label.includes('Display')
              )) {
                targetElement = video.closest('.lk-participant-tile') ||
                              video.closest('[data-lk-participant]') ||
                              video.parentElement;
                console.log("Found screen share tile via video element:", targetElement);
                break;
              }
            }
            if (targetElement) break;
          }
        }
      }

      // Strategy 3: Look for screen share overlay text
      if (!targetElement) {
        const overlays = document.querySelectorAll('.lk-screen-share-overlay');
        if (overlays.length > 0) {
          targetElement = overlays[0].closest('.lk-participant-tile') ||
                        overlays[0].closest('[data-lk-participant]') ||
                        overlays[0].parentElement;
          console.log("Found screen share tile via overlay:", targetElement);
        }
      }

      if (!targetElement) {
        console.error("No screen share tile found. Available elements:", {
          participantTiles: document.querySelectorAll('.lk-participant-tile').length,
          videos: document.querySelectorAll('video').length,
          screenShareIcons: document.querySelectorAll('svg[data-testid="screen-share-icon"]').length
        });
        throw new Error("Screen share tile not found. Make sure screen sharing is active.");
      }

      // Find the video element within the screen share tile
      const videoElement = targetElement.querySelector('video');
      if (!videoElement) {
        throw new Error("No video element found in screen share tile");
      }

      console.log("Found screen share video element:", videoElement);

      // Capture only the video content (the actual shared screen)
      try {
        await captureVideoElement(videoElement);
      } catch (videoError) {
        console.warn("Video capture failed, trying canvas method:", videoError);
        await captureWithCanvas(targetElement);
      }

      // Broadcast to all participants
      await broadcastCaptureEvent();

      // Show success toast
      setToastNotification("Screen share content captured successfully!");
      setToastStatus("success");
      setShowToast(true);

    } catch (error) {
      console.error("Screen capture failed:", error);
      setToastNotification(`Screen capture failed: ${error.message}`);
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsCapturing(false);
    }
  };

  // Debug: Log when component renders
  console.log("ScreenCaptureButton rendered for trackRef:", trackRef);

  return (
    <div className="screen-capture-button">
      <Tooltip title="Capture Screen Share Content" placement="top">
        <Button
          type="primary"
          shape="circle"
          icon={<CameraOutlined />}
          loading={isCapturing}
          onClick={handleScreenCapture}
          size="small"
          className="capture-btn"
          style={{
            backgroundColor: '#1890ff',
            borderColor: '#1890ff'
          }}
        />
      </Tooltip>
    </div>
  );
}
