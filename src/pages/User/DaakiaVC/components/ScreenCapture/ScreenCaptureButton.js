import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { CameraOutlined } from "@ant-design/icons";
import { toPng } from "html-to-image";
import { Track } from "livekit-client";
import { DataReceivedEvent } from "../../utils/constants";
import "./ScreenCaptureButton.scss";

export function ScreenCaptureButton({
  trackRef,
  room,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  const [isCapturing, setIsCapturing] = useState(false);

  // Only show button for screen share tracks
  if (!trackRef || trackRef.source !== Track.Source.ScreenShare) {
    return null;
  }

  const handleScreenCapture = async () => {
    if (isCapturing) return;

    setIsCapturing(true);

    try {
      // Find the screen share tile element - multiple strategies
      let targetElement = null;

      // Strategy 1: Find by participant tile containing screen share video
      const videoElements = document.querySelectorAll('video');
      for (const video of videoElements) {
        if (video.srcObject && video.srcObject.getVideoTracks) {
          const tracks = video.srcObject.getVideoTracks();
          for (const track of tracks) {
            // Check if this is a screen share track
            if (track.label && (
              track.label.includes('screen') ||
              track.label.includes('Screen') ||
              track.label.includes('display') ||
              track.label.includes('Display')
            )) {
              // Find the parent participant tile
              targetElement = video.closest('.lk-participant-tile') ||
                            video.closest('[data-lk-participant]') ||
                            video.parentElement;
              break;
            }
          }
          if (targetElement) break;
        }
      }

      // Strategy 2: Look for elements with screen share metadata
      if (!targetElement) {
        const metadataElements = document.querySelectorAll('[data-lk-participant-metadata]');
        for (const element of metadataElements) {
          const metadata = element.textContent || '';
          if (metadata.includes('screen') || metadata.includes('Screen')) {
            targetElement = element.closest('.lk-participant-tile') || element.parentElement;
            break;
          }
        }
      }

      // Strategy 3: Look for screen share icon in participant tiles
      if (!targetElement) {
        const screenShareIcons = document.querySelectorAll('svg[data-testid="screen-share-icon"], .lk-screen-share-icon');
        if (screenShareIcons.length > 0) {
          targetElement = screenShareIcons[0].closest('.lk-participant-tile') ||
                        screenShareIcons[0].closest('[data-lk-participant]');
        }
      }

      if (!targetElement) {
        throw new Error("Screen share tile not found. Make sure screen sharing is active.");
      }

      await captureElement(targetElement);

      // Broadcast to all participants
      await broadcastCaptureEvent();

      // Show success toast
      setToastNotification("Screen capture taken successfully!");
      setToastStatus("success");
      setShowToast(true);

    } catch (error) {
      console.error("Screen capture failed:", error);
      setToastNotification(`Screen capture failed: ${error.message}`);
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsCapturing(false);
    }
  };

  const captureElement = async (element) => {
    const dataUrl = await toPng(element, {
      quality: 0.95,
      pixelRatio: 1,
      backgroundColor: '#000000',
      filter: (node) => {
        // Exclude the capture button itself from the screenshot
        if (node.classList && node.classList.contains('screen-capture-button')) {
          return false;
        }
        return true;
      }
    });

    // Create download link
    const link = document.createElement('a');
    link.download = `screen-capture-${Date.now()}.png`;
    link.href = dataUrl;
    link.click();
  };

  const broadcastCaptureEvent = async () => {
    if (!room || !room.localParticipant) return;

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.SCREEN_CAPTURE_TAKEN,
        participantName: room.localParticipant.name || room.localParticipant.identity,
        timestamp: Date.now(),
      })
    );

    room.localParticipant.publishData(data, {
      reliable: true,
    });
  };

  return (
    <div className="screen-capture-button">
      <Tooltip title="Capture Screen Share" placement="top">
        <Button
          type="primary"
          shape="circle"
          icon={<CameraOutlined />}
          loading={isCapturing}
          onClick={handleScreenCapture}
          size="small"
          className="capture-btn"
        />
      </Tooltip>
    </div>
  );
}
