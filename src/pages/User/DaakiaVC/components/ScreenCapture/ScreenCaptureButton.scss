.screen-capture-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  
  .capture-btn {
    background-color: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.9);
      transform: scale(1.1);
    }
    
    &:focus {
      background-color: rgba(0, 0, 0, 0.9);
    }
    
    .anticon {
      color: white;
    }
  }
  
  // Hide button on mobile for better UX
  @media (max-width: 768px) {
    top: 5px;
    right: 5px;
    
    .capture-btn {
      width: 28px;
      height: 28px;
      min-width: 28px;
      font-size: 12px;
    }
  }
}

// Ensure the button doesn't interfere with other overlays
.lk-participant-tile {
  position: relative;
  
  .screen-capture-button {
    // Make sure it's above other overlays but below modals
    z-index: 5;
  }
}
