import { <PERSON>, Modal, Pop<PERSON>, Row, Switch, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import "./AudioVideoSettingsModal.scss";
import axios from "axios";
import { datadogLogs } from "@datadog/browser-logs";
import { DeleteOutlined } from "@ant-design/icons";
import { ReactComponent as VisualEffectsIcon } from "./Assets/visualEffects.svg";
import { ReactComponent as BackgroundLightIcon } from "./Assets/bgLight.svg";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
// import { noEffect } from "../../utils/virtualBackground";
import { constants } from "../../utils/constants";
import * as routes from "../../API/Endpoints/routes";
import { getLocalStorageToken } from "../../utils/helper";
// import { SettingsMenuServices } from "../../services/SettingsMenuServices";

export default function VirtualBackgroundModal({
  open,
  setOpen,
  backgrounds,
  setBackgrounds,
}) {
  const [virtualBackgroundName, setVirtualBackgroundName] = useState("None");

  const fetchVirtualBackgrounds = async () => {
    try {
      const response = await SettingsMenuServices.getVirtualBackground(
        // room?.localParticipant?.participantInfo
      );
      if (response.success === 1) {
        const { data } = response;
        const custom = backgrounds.find(
          (category) => category.heading === "Custom"
        );
        data.forEach((item, index) => {
          // Check if virtual_background_id is not null and URL is not present
          if (item.id !== null) {
            const isUrlPresent = custom.effects.some(
              (effect) => effect.icon === item.url
            );
            if (!isUrlPresent) {
              custom.effects.unshift({
                label: `Custom ${index + 1}`,
                icon: item.url,
                value: `CT_${index + 1}`,
                id: item.id, // ensure id is included
              });
            }
          }
        });

        // Update the state with the new backgrounds
        // in backgrounds array[0] is custom background and in that effects array is there a
        setBackgrounds([...backgrounds]);
        // console.log("Backgrounds", backgrounds);
      }
    } catch (error) {
      console.error("Error fetching virtual backgrounds", error);
    }
  };

  useEffect(() => {
    // if (
    //   JSON.parse(room?.localParticipant.metadata)?.role_name === "moderator" &&
    //   // room?.state === "connected"
    // ) {
      fetchVirtualBackgrounds();
    // }
  }, []);

  const handleUpload = async () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";

    fileInput.onchange = async (event) => {
      const file = event.target.files[0];

      if (file) {
        const fileURL = URL.createObjectURL(file); // Create a URL for the file
        // if (
        //   JSON.parse(room?.localParticipant.metadata)?.role_name === "moderator"
        // ) {
          const formData = new FormData();
          formData.append("image", file);
          const response = await axios.post(
            `${constants.STAG_BASE_URL}${routes.Endpoints.set_virtual_background.url}`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                Authorization: `${getLocalStorageToken()}`,
              },
            }
          );
          if (response.data.success === 0) {
            // setToastNotification(
            //   "Something went wrong while upload. Please try again later."
            // );
            // setShowToast(true);
            datadogLogs.logger.error("Error in uploading virtual background",{
              response,
              payload:file,
              endpoint: routes.Endpoints.set_virtual_background.url,
              // user:room?.localParticipant?.participantInfo
            })
            return;
          }else{
            datadogLogs.logger.info("Success in uploading virtual background",{
              response,
              payload:file,
              endpoint: routes.Endpoints.set_virtual_background.url,
              // user:room?.localParticipant?.participantInfo
            })
          }
          
          for (const category of backgrounds) {
            if (category.heading === "Custom") {
              category.effects.unshift({
                label: file.name,
                icon: fileURL,
                value: `CT_${category.effects.length + 1}`,
                id: response.data.data?.id,
              });
            }
          }
        // }else {
        //   for (const category of backgrounds) {
        //     if (category.heading === "Custom") {
        //       category.effects.unshift({
        //         label: file.name,
        //         icon: fileURL,
        //         value: `CT_${category.effects.length + 1}`,
        //         id: category.effects.length + 5,
        //       });
        //     }
        //   }
        // }
        // Apply the virtual background using the uploaded image
        // toggleVirtualBackground(room, fileURL);
        setBackgrounds([...backgrounds]);
        // Optionally, you can clean up the URL after use
        URL.revokeObjectURL(fileURL);
      } else {
        console.log("No file selected");
      }
    };

    fileInput.click(); // Programmatically click the hidden input to open the file dialog
  };

  const handleDelete = async (virtualBackgroundId) => {
    virtualBackgroundId = parseInt(virtualBackgroundId);

    try {
      await axios.delete(
        `${constants.STAG_BASE_URL}${routes.Endpoints.delete_virtual_background.url}`,
        {
          data: {
            virtual_background_id: virtualBackgroundId,
          },
          headers: {
            Authorization: `${getLocalStorageToken()}`,
          },
        }
      );

      for (const category of backgrounds) {
        if (category.heading === "Custom") {
          category.effects = category.effects.filter(
            (effect) => effect.id !== virtualBackgroundId
          );
        }
      }
      setBackgrounds([...backgrounds]);
      // noEffect(room);
    } catch (error) {
      console.error("Error deleting virtual background", error);
    }
  };

  const virtualBackgrounds = (
    <div>
      {backgrounds.map((category) => (
        <div key={category.heading} className="vg-category-container">
          <div className="vg-heading primary-font">
            <span>{category.heading}</span>
          </div>
          <Row gutter={[16, 16]}>
            {category.effects.map((effect) => (
              <Col key={effect.label} xs={24} sm={12} md={8} lg={6}>
                {(category.heading === "Effects" ||
                  category.heading === "Custom") &&
                effect.icon ? (
                  <Tooltip
                    placement="top"
                    title={effect.label}
                    color={"#2db7f5"}
                  >
                    <div
                      className="vg-card"
                      onClick={() => {
                        if (effect.value === 0) {
                          // noEffect(room);
                        } else if (effect.value === "Upload") {
                          handleUpload();
                        } else if (
                          typeof effect.value === "string" &&
                          effect.value?.startsWith("CT_")
                        ) {
                          // toggleVirtualBackground(room, effect.icon);
                        } else {
                          // toggleBlur(room, effect.value);
                        }
                      }}
                    >
                      {(category.heading === "Effects" && effect.icon) ||
                      effect.value === "Upload" ? (
                        <div className="vg-card-image">{effect.icon}</div>
                      ) : (
                        <div>
                          <img
                            alt={effect.label}
                            src={effect.icon}
                            className={`vg-card-image ${effect.id}`}
                          />
                          <span
                            onClick={(e) => {
                              handleDelete(effect.id);
                              e.stopPropagation();
                            }}
                            className="delete-bg"
                          >
                            <DeleteOutlined />
                          </span>
                        </div>
                      )}
                    </div>
                  </Tooltip>
                ) : (
                  <div
                    className="vg-card"
                    onClick={() => {
                      // toggleVirtualBackground(room, effect.value);
                      setVirtualBackgroundName(effect.label);
                    }}
                  >
                    <img
                      alt={effect.label}
                      src={effect.icon ? effect.icon : effect.value}
                      className="vg-card-image"
                    />
                  </div>
                )}
              </Col>
            ))}
          </Row>
        </div>
      ))}
    </div>
  );

  return (
    <Modal
      open={open}
      onOk={() => setOpen(false)}
      onCancel={() => setOpen(false)}
      className="visual-effects-modal"
      footer={null}
    >
      <div className="visual-effects-modal-options">
        <h4>Visual Effects</h4>
        <div className="visual-effects-modal-options-dropdown">
          <span className="device-name">Virtual Background</span>
          <Popover
            content={virtualBackgrounds}
            trigger="click"
            placement="bottom"
            overlayClassName="virtual-background-popover"
          >
            <div className="visual-effects-modal-options-select visual-effects">
              {virtualBackgroundName}
            </div>
          </Popover>
        </div>
      </div>
      <div className="visual-effects-modal-options">
        <span className="device-name">Blur Background</span>
        <div className="visual-effects-modal-options-select">
          <div className="visual-effects-modal-options-select-option">
            <input type="checkbox" />
            <label>no blur</label>
          </div>
          <div className="visual-effects-modal-options-select-option">
            <input type="checkbox" />
            <label>light blur</label>
          </div>
          <div className="visual-effects-modal-options-select-option">
            <input type="checkbox" />
            <label>heavy blur</label>
          </div>
        </div>
      </div>
      <div className="visual-effects-modal-options">
        <VisualEffectsIcon className="audio-video-settings-modal-icon" />
        <div className="audio-video-settings-modal-device-options">
          <span className="device-name">Adjust Face Brightness</span>
          <Switch />
        </div>
      </div>
      <div className="visual-effects-modal-options">
        <BackgroundLightIcon className="audio-video-settings-modal-icon" />
        <div className="audio-video-settings-modal-device-options">
          <span className="device-name">Adjust Background Brightness</span>
          <Switch />
        </div>
      </div>
    </Modal>
  );
}
