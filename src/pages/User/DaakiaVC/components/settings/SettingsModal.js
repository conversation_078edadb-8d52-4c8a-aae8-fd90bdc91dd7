import { Modal, Switch, Tabs } from "antd";
import React from "react";
import "../../styles/SettingModal.scss";
import { PiVideoCameraFill } from "react-icons/pi";
import { ReactComponent as VoiceIcon } from "./icons/VoiceIco.svg";

export default function SettingsModal({
  isSettingsModalOpen,
  setIsSettingsModalOpen,
  room,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
}) {
  const [noiseCancellation, setNoiseCancellation] = React.useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression
  );
  const [echoCancellation, setEchoCancellation] = React.useState(
    room?.options?.audioCaptureDefaults?.echoCancellation
  );

  const handleNoiseCancellation = () => {
    setNoiseCancellation((prevNoiseCancellation) => {
      const newNoiseCancellation = !prevNoiseCancellation;
      // Update room option for noise suppression
      if (room?.options?.audioCaptureDefaults) {
        room.options.audioCaptureDefaults.noiseSuppression =
          newNoiseCancellation;
      }
      return newNoiseCancellation;
    });
  };

  const handleEchoCancellation = () => {
    setEchoCancellation((prevEchoCancellation) => {
      const newEchoCancellation = !prevEchoCancellation;
      // Update room option for echo cancellation
      if (room?.options?.audioCaptureDefaults) {
        room.options.audioCaptureDefaults.echoCancellation =
          newEchoCancellation;
      }
      return newEchoCancellation;
    });
  };

  const items = [
    {
      key: "1",
      label: (
        <div className="tab-label">
          <VoiceIcon />
          <span>Voice</span>
        </div>
      ),
      children: (
        <div className="voice-settings">
          <h2>Voice Settings</h2>
          <div className="voice-settings-options">
            <div>
              <div className="voice-settings-options-text">
                <p>Noise cancellation</p>
                <span>
                  Remove background noise from your audio input to improve the
                  call quality.
                </span>
              </div>
              <Switch
                checked={noiseCancellation}
                onChange={handleNoiseCancellation}
              />
            </div>
            <div>
              <div className="voice-settings-options-text">
                <p>Echo cancellation</p>
                <span>
                  Remove sound echo from your audio input to improve the call
                  quality.
                </span>
              </div>
              <Switch
                checked={echoCancellation}
                onChange={handleEchoCancellation}
              />
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "2",
      label: (
        <div className="tab-label">
          <PiVideoCameraFill />
          <span>Video</span>
        </div>
      ),
      children: (
        <div className="voice-settings">
          <h2>Video Settings</h2>
          <div className="voice-settings-options">
            <div>
              <div className="voice-settings-options-text">
                <p>Mirror video</p>
                <span>
                  Flip your video horizontally to correct the orientation.
                </span>
              </div>
              <Switch
                checked={isSelfVideoMirrored}
                onChange={() => setIsSelfVideoMirrored((prev) => !prev)}
              />
            </div>
          </div>
        </div>
      ),
    }
  ];

  return (
    <Modal
      className="settings-modal"
      title="Settings"
      open={isSettingsModalOpen}
      onOk={() => setIsSettingsModalOpen(false)}
      onCancel={() => setIsSettingsModalOpen(false)}
      onClose={() => setIsSettingsModalOpen(false)}
    >
      <h3 className="ant-modal-title">Settings</h3>
      <Tabs defaultActiveKey="1" items={items} tabPosition="left" className="settings-tabs" />
    </Modal>
  );
}
