@import "./variables";

.settings {
  &-tabs{
    .ant-tabs-nav-list{
      width: 8.85rem;
    }
  }
  &-modal {
    width: 50vw !important;
    height: 60vh;
    border-radius: 10px;
    padding: 0;
    @media screen and (max-width: 1200px) {
      width: 60vw !important;
    }
    @media screen and (max-width: 1000px) {
      width: 70vw !important;
    }
    @media screen and (max-width: 800px) {
      width: 80vw !important;
    }

    .ant-modal-title {
      color: #000;
      font-family: $font;
      font-weight: bold;
      font-size: 22px;
      width: auto;
      display: inline-block;
      border-right: 2px solid #eff1f4;
      padding: 1rem 1.65rem;
    }
    .ant-modal-header {
      display: none;
    }
    .ant-modal-footer {
      display: none;
    }
    .ant-modal-body {
      padding: 0;
      height: 100%;
      position: relative;
      overflow: hidden; //change in future if needed
      .ant-tabs {
        height: 100%;
      }
      .ant-tabs-tab-active {
        background-color: #d5eaff;
        padding: 0.7rem 1.15rem;
      }
      .tab-label {
        font-family: $font;
        font-weight: 500;
        font-size: 22px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      .ant-tabs-ink-bar {
        display: none;
      }
      .voice-settings {
        display: flex;
        flex-direction: column;
        width: 95%;
        h2 {
          font-size: 22px;
          font-family: $font;
          font-weight: 600;
          color: #000;
          padding-bottom: 0.2rem;
          border-bottom: 2px solid #eff1f4;
        }
        &-options {
          display: flex;
          flex-direction: column;
          padding: 2rem 0;
          gap: 1rem;
          &-text {
            display: flex;
            flex-direction: column;
            p {
              font-size: 1.2rem;
              font-family: $font;
              font-weight: 500;
              color: #000;
              margin: 0;
            }
          }
          div {
            display: flex;
            justify-content: space-between;
            .ant-switch-checked {
              background-color: #277bf7;
            }
          }
        }
      }
    }
    .ant-modal-content {
      border-radius: 10px !important;
      height: 100%;
      .ant-tabs-content-holder {
        border-left: 2px solid #eff1f4;
      }
    }
  }
}
