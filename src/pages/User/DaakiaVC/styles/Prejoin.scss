@import "./mixins";
@import "./variables";

.prejoin-parent-container {
  @include pre-join-inner-container;
  height: 100svh;
  font-size: 16px;

  @media screen and (max-width: 480px) {
    padding-top: 5rem;
  }
  
  .prejoin-logo{
    position: absolute;
    left: 3rem;
    top: 0;
    &-electron{
      top: 1.5rem;
      left: 1rem;
    }
    @media screen and (max-width: 480px){
      left: 1rem;
      top: 0.5rem;
    }
    svg{
      width: 90px;
      height: 80px;
      @media screen and (max-width: 480px){
        width: 50px;
        height: 40px;
      }
    }
  }

  .prejoin-left-inner-container {
    @include pre-join-inner-container;
    width: 100%;
    height: 100%;

    .prejoin-mobile-view-heading {
      display: none;
      @media screen and (max-width: 480px) {
        margin-top: 2rem;
      }
    }

    .prejoin-left-inner-description {
      color: $prejoin-left-inner-description-color;
      font-size: 1em;
      font-weight: 400;
      margin-top: 1rem;

      @media (max-width: 1200px) {
        margin-top: 0.8rem;
      }

      @media (max-width: 768px) {
        margin-top: 0.5rem;
      }

      @media (max-width: 480px) {
        margin-top: 0.3rem;
      }
    }

    .lk-prejoin {
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2),
        0 6px 20px 0 rgba(0, 0, 0, 0.19);
      border-radius: 10px;
    }
  }

  .prejoin-right-inner-container {
    @include pre-join-inner-container;
    width: 100%;
    height: 100%;

    .container-parent-form {
      width: 100%;
      height: 100%;

      .join-meeting-title {
        font-size: 2.3rem;
        font-weight: 400;
        color: $join-meeting-title-color;
        margin-bottom: 20px;
        font-family: $font;

        @media (max-width: 1200px) {
          font-size: 1.8rem !important;
        }

        @media (max-width: 940px) {
          font-size: 1.5rem !important;
        }

        @media (max-width: 1040px) {
          font-size: 1.7rem;
        }

        @media (max-width: 890px) {
          font-size: 1.5rem;
        }

        @media (max-width: 785px) {
          font-size: 1.3rem;
        }
      }

      .form-input-container {
        margin-top: 2rem;
        width: 75%;
        margin-bottom: 20px;

        p {
          font-size: 1rem;
          font-weight: 500;
          color: $participant-name-title-color;
          margin-bottom: 0.5rem;
          font-family: $font;
          font-family: $font;
        }

        .ant-input-lg {
          font-size: 20px;
          font-weight: 600;
          font-family: $font;
          color: $participant-name-color;
        }
      }
    }
  }
}

.container-parent-form {
  .button-container {
    display: flex;
    justify-content: flex-end;
    width: 75%;
    margin-top: 40px;
    gap: 1rem;

    .go-back-link{
      display: flex;
      align-items: center;
      text-decoration: underline;
      font-size: 18px;
    }

    .join-button-disabled {
      @include pre-join-button;
      background-color: $join-button-disabled;
      pointer-events: none; /* Make the button not clickable */
      cursor: not-allowed; /* Show a "not-allowed" cursor */

      span {
        font-family: $font;
      }
    }

    .join-button-enabled {
      @include pre-join-button;
      background-color: $join-button-enabled;
      box-shadow: 0 4px 8px 0 #b9dcff, 0 1px 2px 0 #c8ccd2;

      span {
        font-family: $font;
      }
    }
  }
}

@media (max-width: 480px) {
  .prejoin-parent-container {
    .ant-row {
      flex-direction: column;
      flex-wrap: nowrap;
      align-items: center;
      position: relative;
      .prejoin-left-inner-container {
        max-width: 90%;
        .prejoin-mobile-view-heading {
          display: block;
          font-size: 1.2rem;
          font-weight: 600;
          color: #000000;
          margin-bottom: 20px;
        }
        .button-icon {
          svg {
            width: 18px;
            height: 20px;
          }
        }
      }
      .prejoin-right-inner-container {
        max-width: 90%;
        .container-parent-form {
          display: flex;
          flex-direction: column;
          align-items: center;
          .form-input-container {
            p {
              font-size: 1rem;
            }
          }
          .join-meeting-title {
            display: none;
          }
          .button-container {
            width: auto;
            margin-top: 1rem;
          }
        }
      }
    }
  }
}

@media (max-width: 1040px) {
  .form-input-container p {
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
  }
}

@media (max-width: 890px) {
  .form-input-container p {
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
  }
}

@media (max-width: 785px) {
  .form-input-container p {
    font-size: 0.7rem;
    margin-bottom: 0.2rem;
  }
}

// PrejoinAudioVideo.js
.button-icon {
  padding: 8px 10px;
  svg {
    width: 31px;
    height: 30px;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.not-allowed {
  .loading-title {
    @media screen and (max-width: 1200px) {
      font-size: 1.6rem;
    }
    @media screen and (max-width: 960px) {
      font-size: 1.2rem;
    }
  }
}

@media screen and (max-width: 450px) {
  .loading-title {
    text-align: center !important;
    font-size: 1.2rem !important;
  }
  .loading-description {
    text-align: center !important;
  }
}

// Loader.js
.loading-parent-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .loading-title {
    font-size: 2rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 20px;
    font-family: $font;
  }
  .loading-description {
    color: #000000;
    font-size: 1em;
    font-weight: 400;
    margin-bottom: 20px;
    font-family: $font;
  }
  .rotate {
    animation: rotate 1s linear infinite;
    width: 31px;
    height: 30px;
  }
}
.prejoin-toast {
  .toast{
    width: auto;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #2b2b2b;
    padding: 5px 10px;
    color: white;
    font-size: 12px; 
    border-radius: 3px; 
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); 
    z-index: 9999;
    max-width: 200px; 
    text-align: center; 
    
  }
}


// PrejoinAudioVideo.js

.lk-prejoin{
  position: relative;
  .lk-button-group{
    &-menu{
      width: 2.5rem;
      background-color: #181818;
      border-radius: 0 10px 10px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      svg{
        width: 1.5rem;
        height: 1.5rem;
        color: #888888;
      }
    }
  }
}
.mirrored-video {
  transform: scaleX(-1) !important;
  -webkit-transform: scaleX(-1) !important; /* For older browsers */
}
.not-mirrored-video{
  transform: scaleX(1) !important;
  -webkit-transform: scaleX(1) !important; /* For older browsers */
}