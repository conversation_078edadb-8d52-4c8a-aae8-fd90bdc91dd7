import { Track } from "livekit-client";
import { BackgroundBlur, VirtualBackground } from "@livekit/track-processors";

export async function toggleBlur(currentRoom, blur) {
  if (!currentRoom) return;

  try {
    const camTrackPublication =
      currentRoom.localParticipant.getTrackPublication(Track.Source.Camera);
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");
    if (
      camTrack.getProcessor() &&
      camTrack.getProcessor().name !== "background-blur"
    ) {
      await camTrack.stopProcessor();
    }
    await camTrack.setProcessor(BackgroundBlur(blur, { delegate: "GPU" }));
  } catch (e) {
    console.log("MyError1:", e);
    console.log(`ERROR: ${e.message}`);
  }
}

export async function toggleVirtualBackground(
  currentRoom,
  background
) {
  if (!currentRoom) return;

  try {
    const camTrackPublication =
      currentRoom.localParticipant.getTrackPublication(Track.Source.Camera);
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    if (
      camTrack.getProcessor() &&
      camTrack.getProcessor().name !== "virtual-background"
    ) {
      await camTrack.stopProcessor();
    }
    // console.log("Background:", background);
    await camTrack.setProcessor(VirtualBackground(background));
  } catch (e) {
    console.log("MyError2:", e);
    console.log(`ERROR: ${e.message}`);
  }
}

export async function noEffect(room) {
  if (!room) return;
  try {
    const camTrackPublication = room.localParticipant.getTrackPublication(
      Track.Source.Camera
    );
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    if (camTrack.getProcessor()) {
      await camTrack.stopProcessor();
    }
  } catch (e) {
    console.log("MyError3:", e);
    console.log(`ERROR: ${e.message}`);
  }
}
