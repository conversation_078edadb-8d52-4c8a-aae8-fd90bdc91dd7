/* eslint-disable */
import React, { useState, useCallback, useEffect } from "react";
import { Row, Col, Input, Button } from "antd";
import { EyeTwoTone, EyeInvisibleOutlined } from "@ant-design/icons";
import { datadogLogs } from "@datadog/browser-logs";

import axios from "axios";

import { PreJoinAudioVideo } from "./PreJoinAudioVideo";
import { constants } from "../utils/constants";
import { ReactComponent as <PERSON>akiaLogo } from "./icons/DaakiaLogoDark.svg";

import { getLocalStorage } from "../utils/helper";
import { PrejoinService } from "../services/PrejoinServices";

import "../styles/Prejoin.scss";
import "../styles/index.scss";
import { Loader } from "../components/Loader";
import { Link } from "react-router-dom";
import isElectron from "is-electron";
import TitleBar from "../components/titleBar";
import { Toast } from "react-bootstrap";
import { VirtualBackgroundDrawer } from "../components/settings/VirtualBackgroundDrawer";
import StatusNotification from "../components/StatusNotification/StatusNotification";
// import { VideoConferenceService } from "../../../../services"; // Need to bring inside

export function Prejoin({
  setServerDetails,
  id,
  setPreJoinShow,
  isHost,
  isPasswordProtected,
  meetingDetails,
  setClientPreferedServerId,
  userChoices,
  setUserChoices,
  isLobbyMode,
  isWebinarMode,
  setIsPipWindow,
  isPipWindow,
  room,
  backgrounds,
  setBackgrounds,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  deviceIdAudio,
  setDeviceIdAudio,
  deviceIdVideo,
  setDeviceIdVideo,
}) {
  const [username, setUsername] = useState(
    isHost ? meetingDetails.host : userChoices.username || ""
  );
  const [password, setPassword] = useState("");
  const [isValid, setIsValid] = useState(false);
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [canJoin, setCanJoin] = useState(true);
  const [isRejected, setIsRejected] = useState(false);
  const [isMeetingStarted, setIsMeetingStarted] = useState(false);
  const [cohostDetail, setCohostDetail] = useState(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastStatus, setToastStatus] = useState("");

  // const { notify, contextHolder } = useMessageNotification();

  const getRegion = useCallback(async () => {
    const maxAttempts = 3;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const response = await axios.get(constants.REGION_LOCATOR);
        setClientPreferedServerId(
          () => response?.data?.preferred_video_server_id
        );
        return response;
      } catch (error) {
        setToastMessage(error.message);
        setToastStatus("error");
        setShowToast(true);
        datadogLogs.logger.error(
          `Error getting region (attempt ${attempts + 1})`,
          {
            error,
          }
        );
        attempts += 1;
        if (attempts >= maxAttempts) {
          datadogLogs.logger.error(
            `Error getting region (attempt ${attempts + 1})`,
            {
              error,
              message: "Setting up default server to ap1",
            }
          );
          return { data: { preferred_video_server_id: "ap1" } };
        }
      }
    }
  }, []);

  useEffect(() => {
    try {
      const detail = getLocalStorage(constants.CO_HOST_TOKEN);
      if (detail?.current_session_uid !== meetingDetails?.current_session_uid) {
        localStorage.removeItem(constants.CO_HOST_TOKEN);
        setCohostDetail(null);
      } else {
        setCohostDetail(detail);
      }
    } catch (error) {
      datadogLogs.logger.error(
        `Error getting cohost detail from local storage`,
        {
          error,
        }
      );
    }
  }, [meetingDetails?.current_session_uid]);

  const getServerDetails = useCallback(
    async (user) => {
      if (!isValid) return;
      setIsLoading(true);
      try {
        if (
          isPasswordProtected &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          const validatePassword = await PrejoinService.verifyPassword(
            email,
            password,
            id
          );
          if (validatePassword.success === 0) {
            console.log("validatePassword", validatePassword);
            setToastMessage("Email or Password Incorrect");
            setToastStatus("error");
            setShowToast(true);
            setIsLoading(false);
            return;
          }
        }
        if (
          meetingDetails?.is_common_password &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          const validatePassword = await PrejoinService.commonPasswordVerify(
            id,
            password
          );
          if (validatePassword.success === 0) {
            setToastMessage("Email or Password Incorrect");
            setToastStatus("error");
            setShowToast(true);
            setIsLoading(false);
            return;
          }
        }
        const region = await getRegion();
        let joinMeetingPayload = {
          preferred_video_server_id:
            region && region.data && region.data.preferred_video_server_id
              ? region.data.preferred_video_server_id
              : "ap1",
          meeting_uid: id,
          display_name: user.username,
        };
        if (
          isLobbyMode &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          const lobbyResponse = await PrejoinService.addParticipantToLobby(
            id,
            user.username
          );

          if (lobbyResponse.success === 0) {
            // notify("error", lobbyResponse.message);
            setToastMessage(lobbyResponse.message);
            setToastStatus("error");
            setShowToast(true);
            setIsLoading(false);
            return;
          }
          joinMeetingPayload = {
            ...joinMeetingPayload,
            lobby_request_id: lobbyResponse.data.request_id,
          };
        }
        if (
          cohostDetail?.current_session_uid ===
          meetingDetails?.current_session_uid
        ) {
          joinMeetingPayload = {
            ...joinMeetingPayload,
            meeting_attendance_uid: String(cohostDetail?.meeting_attendance_id),
          };
        }
        const response = await PrejoinService.joinMeeting(joinMeetingPayload, {
          username,
          isHost,
        });

        if (response?.success === 0) {
          // notify("error", response.message);
          setToastMessage(response.message);
          setToastStatus("error");
          setShowToast(true);
          setIsLoading(false);
          return;
        }

        if (
          !response.data.participant_can_join &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          // if (!isLobbyMode) {
          //   notify("error", "Meeting has not started yet");
          // }
          if (
            (isLobbyMode || isWebinarMode) &&
            response.data?.meeting_started
          ) {
            setIsMeetingStarted(true);
          }
          setCanJoin(false);
          const intervalId = setInterval(async () => {
            try {
              const pollingResponse = await PrejoinService.joinMeeting(
                joinMeetingPayload,
                {
                  username,
                  isHost,
                }
              );

              if (
                (isLobbyMode || isWebinarMode) &&
                pollingResponse.data?.meeting_started
              ) {
                setIsMeetingStarted(true);
              }

              if (isLobbyMode && pollingResponse.data?.is_rejected) {
                clearInterval(intervalId);
                // notify("error", pollingResponse.message);
                setCanJoin(false);
                setIsRejected(true);
              }
              if (pollingResponse.data.participant_can_join) {
                clearInterval(intervalId);
                setServerDetails(() => ({
                  serverUrl: pollingResponse.data.livekit_server_URL,
                  token: pollingResponse.data.access_token,
                }));
                setPreJoinShow(() => false);
                setCanJoin(true);
              }
            } catch (error) {
              setToastMessage(error.message);
              setToastStatus("error");                
              setShowToast(true);
              // notify("error", "Something went wrong, please try again later");
            }
          }, 5000);
          return;
        }
        setServerDetails(() => ({
          serverUrl: response.data.livekit_server_URL,
          token: response.data.access_token,
        }));

        setPreJoinShow(() => false);
        setCanJoin(() => true);
      } catch (error) {
        setToastMessage(error?.message);
        setToastStatus("error")
        setShowToast(true);
      } finally {
        setIsLoading(false);
      }
    },
    [
      getRegion,
      id,
      username,
      isPasswordProtected,
      isValid,
      email,
      password,
      // notify,
      setPreJoinShow,
      setServerDetails,
    ]
  );

  const handleJoinMeeting = useCallback(
    (user) => {
      getServerDetails(user);
    },
    [getServerDetails]
  );

  const handleValidation = useCallback(() => {
    const trimmedUsername = username.trim();
    const trimmedPassword = password.trim();
    const trimmedEmail = email.trim();

    if (
      trimmedUsername === "" ||
      trimmedPassword === "" ||
      trimmedEmail === ""
    ) {
      setIsValid(false);
      return false;
    }
    
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/;

    if (!emailPattern.test(trimmedEmail)) {
      setIsValid(false);
      return false;
    }

    setIsValid(true);
    return true;
  }, [username, password, email]);

  const handleCommonPasswordValidation = useCallback(() => {
    const trimmedUsername = username.trim();
    const trimmedPassword = password.trim();

    if (trimmedUsername === "" || trimmedPassword === "") {
      setIsValid(false);
      return false;
    }

    setIsValid(true);
    return true;
  }, [username, password]);

  useEffect(() => {
    if (
      isPasswordProtected &&
      !isHost &&
      cohostDetail?.current_session_uid !== meetingDetails?.current_session_uid
    )
      handleValidation();

    if (
      meetingDetails?.is_common_password &&
      !isHost &&
      cohostDetail?.current_session_uid !== meetingDetails?.current_session_uid
    ) {
      handleCommonPasswordValidation();
    }

    if (
      (meetingDetails?.is_common_password || isPasswordProtected) &&
      (isHost ||
        cohostDetail?.current_session_uid ===
          meetingDetails?.current_session_uid)
    ) {
      setIsValid(true);
    }
  }, [
    username,
    password,
    email,
    handleValidation,
    isPasswordProtected,
    isHost,
    cohostDetail,
  ]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (showToast) setShowToast(false);
    }, 5000);

    // Clear the timeout if the component unmounts or if showToast changes
    return () => clearTimeout(timer);
  }, [showToast]);

  return (
    <div className="prejoin-parent-container">
      {isElectron() && (
        <TitleBar
          title={meetingDetails?.event_name}
          pipEnabled={false}
          setIsPipWindow={setIsPipWindow}
          isPipWindow={isPipWindow}
        />
      )}
      <div
        className={`prejoin-logo ${isElectron() && "prejoin-logo-electron"}`}
      >
        <DaakiaLogo />
      </div>
      {/* {contextHolder} */}
      <Row gutter={[16, 24]} style={{ width: "100%" }}>
        <Col span={12} className="prejoin-left-inner-container">
          <div className="prejoin-mobile-view-heading">
            You are about to join a Daakia Call
          </div>
          <PreJoinAudioVideo
            defaults={userChoices}
            username={username}
            setIsValid={setIsValid}
            setUserChoices={setUserChoices}
            onSubmit={handleJoinMeeting}
            // setToastNotification={setToastNotification}
            setToastMessage={setToastMessage}
            setToastStatus={setToastStatus}
            setShowToast={setShowToast}
            data-lk-theme="default"
            onValidate={
              isPasswordProtected &&
              !isHost &&
              cohostDetail?.current_session_uid !==
                meetingDetails?.current_session_uid
                ? handleValidation
                : null
            }
            room={room}
            backgrounds={backgrounds}
            setBackgrounds={setBackgrounds}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            deviceIdAudio={deviceIdAudio}
            setDeviceIdAudio={setDeviceIdAudio}
            deviceIdVideo={deviceIdVideo}
            setDeviceIdVideo={setDeviceIdVideo}
          />
          <div className="primary-font prejoin-left-inner-description">
            Please check your audio/video settings
          </div>
        </Col>
        <Col span={12} className="prejoin-right-inner-container primary-font">
          {isLoading ? (
            <Loader
              heading="Preparing to join...."
              description="You will be able to join shortly"
              isLoading
            />
          ) : isRejected &&
            !canJoin &&
            !isHost &&
            cohostDetail?.current_session_uid !==
              meetingDetails?.current_session_uid ? (
            <div className="not-allowed">
              <Loader
                heading="You are not allowed to join the meeting"
                description="Please contact the host to join the meeting"
                isLoading={false}
              />
            </div>
          ) : isLobbyMode &&
            !canJoin &&
            !isHost &&
            cohostDetail?.current_session_uid !==
              meetingDetails?.current_session_uid &&
            isMeetingStarted ? (
            <Loader
              heading="Asking the host to join...."
              description="Please wait for a moment"
              isLoading
            />
          ) : !isMeetingStarted &&
            !canJoin &&
            !isHost &&
            cohostDetail?.current_session_uid !==
              meetingDetails?.current_session_uid ? (
            <Loader
              heading="Waiting to join...."
              description={`Please wait for the host to start the ${
                isWebinarMode ? "webinar" : "meeting"
              }`}
              isLoading
            />
          ) : (
            <div className="container-parent-form">
              <p className="join-meeting-title">
                You are about to join a Daakia Call
              </p>
              <div className="form-input-container">
                <p>
                  Your Name<span className="text-danger">*</span>
                </p>
                <Input
                  size="large"
                  disabled={isHost}
                  placeholder="Your Name"
                  value={username}
                  onChange={(e) => {
                    setUserChoices({
                      ...userChoices,
                      username: e.target.value,
                    });
                    setUsername(e.target.value);
                  }}
                  onKeyDown={(e) => {
                    !isPasswordProtected &&
                      e.key === "Enter" &&
                      handleJoinMeeting(userChoices);
                  }}
                />
              </div>
              {isPasswordProtected && !isHost && (
                <>
                  <div className="form-input-container">
                    <p>
                      Email<span className="text-danger">*</span>
                    </p>
                    <Input
                      size="large"
                      placeholder="Email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                  <div className="form-input-container">
                    <p>
                      Password<span className="text-danger">*</span>
                    </p>
                    <Input.Password
                      size="large"
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      iconRender={(visible) =>
                        visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                      }
                    />
                  </div>
                </>
              )}
              {meetingDetails?.is_common_password &&
                !isHost &&
                cohostDetail?.current_session_uid !==
                  meetingDetails?.current_session_uid && (
                  <div className="form-input-container">
                    <p>
                      Password<span className="text-danger">*</span>
                    </p>
                    <Input.Password
                      size="large"
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      iconRender={(visible) =>
                        visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                      }
                    />
                  </div>
                )}
              <div className="button-container">
                {/* {isElectron() && (
                  <Link
                    to="/video-conferencing"
                    className="go-back-link"
                  ><IoArrowBackOutline /> Go back</Link>
                )} */}
                <Button
                  type="primary"
                  size="large"
                  className={
                    isValid ? "join-button-enabled" : "join-button-disabled"
                  }
                  onClick={() => handleJoinMeeting(userChoices)}
                >
                  Join Meeting
                </Button>
              </div>
            </div>
          )}
        </Col>
      </Row>
      {showToast && (
        <StatusNotification
          status={toastStatus}
          message={toastMessage}
        />
      )}
    </div>
  );
}
