import { Track } from "livekit-client";
import {
  FocusLayoutContainer,
  LayoutContextProvider,
  TrackRefContext,
  RoomAudioRenderer,
  MediaDeviceMenu,
  usePersistentUserChoices,
  DisconnectButton,
} from "@livekit/components-react";
import React, { useState, useEffect, useCallback } from "react";
import { ParticipantTile } from "./ParticipantTile";
import "../styles/VideoConference.scss";
import { onDeviceError } from "../utils/helper";
import { TrackToggle } from "../components/TrackToggle";
import { ReactComponent as LeaveIcon } from "../assets/icons/LeaveIcon.svg";

export default function PipLayout({
  layoutContext,
  carouselTracks,
  isForceMuteAll,
  isCoHost,
  isHost,
  isForceVideoOffAll,
  saveUserChoices = true,
  // setIsPipWindow,
  // isPipWindow,
  isScreenShareEnabled,
  // setIsScreenShareEnabled,
  onScreenShareChange,
  screenShareMode,
  maxWidth,
  maxHeight,
}) {
  // State to track the participant who is speaking
  const [speakingParticipant, setSpeakingParticipant] = useState(null);

  // Validate carouselTracks
  const validCarouselTracks = carouselTracks.filter(
    (track) => track && track.participant && track.participant.identity
  );

  const {
    saveAudioInputEnabled,
    saveVideoInputEnabled,
    saveAudioInputDeviceId,
    saveVideoInputDeviceId,
  } = usePersistentUserChoices({ preventSave: !saveUserChoices });

  const microphoneOnChange = useCallback(
    (enabled, isUserInitiated) =>
      isUserInitiated ? saveAudioInputEnabled(enabled) : null,
    [saveAudioInputEnabled]
  );

  const cameraOnChange = useCallback(
    (enabled, isUserInitiated) =>
      isUserInitiated ? saveVideoInputEnabled(enabled) : null,
    [saveVideoInputEnabled]
  );

  // Track the first speaking participant when carouselTracks changes
  useEffect(() => {
    // Find the first participant who is speaking
    const speaking = validCarouselTracks.find(
      (track) => track?.participant?.isSpeaking
    );

    if (speaking) {
      setSpeakingParticipant(speaking);
    } else {
      setSpeakingParticipant(null); // Reset if no one is speaking
    }
  }, [carouselTracks]); // Re-run effect whenever carouselTracks changes

  return (
    <div
      className="pip-layout-container"
      style={{
        height: "100svh",
        display: "flex",
        flexDirection: "column",
        position: "relative",
        paddingBottom: "10px",
      }}
    >
      <LayoutContextProvider value={layoutContext}>
        <FocusLayoutContainer>
          {/* Render carousel participants */}
          {validCarouselTracks.map((track, index) => {
            const isSpeaking = track?.participant?.isSpeaking;

            return (
              <TrackRefContext.Provider
                value={track}
                key={track.participant.identity || `track-${index}`}
              >
                {isSpeaking ? (
                  // Show the participant who is speaking with special styling
                  <ParticipantTile trackRef={track} />
                ) : // Render participant normally if not speaking
                // <ParticipantTile trackRef={track} />
                null}
              </TrackRefContext.Provider>
            );
          })}

          {/* If no one is speaking, render the first participant normally */}
          {speakingParticipant === null && validCarouselTracks.length > 0 && (
            <TrackRefContext.Provider
              value={validCarouselTracks[0]}
              key={validCarouselTracks[0]?.participant.identity || `track-0`}
            >
              <ParticipantTile trackRef={validCarouselTracks[0]} />
            </TrackRefContext.Provider>
          )}
        </FocusLayoutContainer>
        <div className="control-bar-container">
          <div
            className={`lk-button-group ${
              isForceMuteAll && (!isCoHost || !isHost) ? "disabled" : ""
            }`}
          >
            <TrackToggle
              source={Track.Source.Microphone}
              showIcon
              onChange={microphoneOnChange}
              className="control-bar-button control-bar-button-icon"
              onDeviceError={onDeviceError}
            />

            <div className="lk-button-group-menu">
              <MediaDeviceMenu
                kind="audioinput"
                onActiveDeviceChange={(_kind, deviceId) =>
                  saveAudioInputDeviceId(deviceId ?? "")
                }
              />
            </div>
          </div>

          <div
            className={`lk-button-group ${
              isForceVideoOffAll && (!isCoHost || !isHost) ? "disabled" : ""
            }`}
          >
            <TrackToggle
              source={Track.Source.Camera}
              showIcon
              onChange={cameraOnChange}
              className="control-bar-button-camera control-bar-button control-bar-button-icon"
              onDeviceError={onDeviceError}
            />
            <div className="lk-button-group-menu">
              <MediaDeviceMenu
                kind="videoinput"
                onActiveDeviceChange={(_kind, deviceId) =>
                  saveVideoInputDeviceId(deviceId ?? "")
                }
              />
            </div>
          </div>

          {/* ScreenShare Button */}
          {isScreenShareEnabled && (
            <TrackToggle
              source={Track.Source.ScreenShare}
              captureOptions={
                screenShareMode === "text"
                  ? {
                      audio: true,
                      contentHint: "detail",
                      resolution: {
                        width: maxWidth,
                        height: maxHeight,
                        frameRate: 5,
                      },
                    }
                  : {
                      audio: true,
                      contentHint: "detail",
                      resolution: {
                        width: maxWidth,
                        height: maxHeight,
                        frameRate: 30,
                      },
                    }
              }
              showIcon
              onChange={onScreenShareChange}
              // onClick={() => setIsPIPEnabled(false)}
              onDeviceError={onDeviceError}
              className="control-bar-button control-bar-button-icon"
            />
          )}

          {/* Disconnect Button */}
          <DisconnectButton
            className="control-bar-button control-bar-button-icon"
            style={{ backgroundColor: "rgba(255, 59, 48, 1)" }}
            onClick={() => {
              window?.electronAPI?.ipcRenderer?.send("stop-annotation");
              window?.electronAPI?.ipcRenderer?.send("close-window");
            }}
          >
            <LeaveIcon />
            {/* {showText && "Leave"} */}
          </DisconnectButton>
        </div>
      </LayoutContextProvider>
      <RoomAudioRenderer />
    </div>
  );
}
